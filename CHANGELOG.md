# Changelog

## [0.33] - 2025-06-26

### Removed
- Odstránené zobrazenie profilových fotografií používateľov z profilu
- Odstránená automatická integrácia s Gravatar pre profilové fotografie
- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pole `photoURL` z rozhrania `AuthUser`

### Changed
- Upravený dizajn profilu používateľa bez sekcie s fotografiou
- Zjednodušená hlavička profilu s lepším rozložením textu

---

# Changelog - Implementácia OpenStreetMap pre adresy

## Prehľad zmien

Táto aktualizácia implementuje nasledujúce zmeny:

1. Odstránenie možnosti nastavenia lokality pri hračkách (pri vytvorení, editácii a v DB)
2. Implementácia OpenStreetMap pre vyhľadávanie presnej adresy
3. Ukladanie geografických súradníc pre možné filtrovanie hračiek v určitom okruhu
4. Umožnenie používateľovi nastaviť adresu v profile pomocou API OpenStreetMap
5. Zobrazenie nastavenej lokality v detaile hračiek

## Detaily implementácie

### 1. Úprava databázového modelu

- Pridané polia `latitude` a `longitude` do modelu `User` pre uloženie geografických súradníc
- Odstránený vzťah medzi hračkami a lokalitami (odstránené pole `locationId` z modelu `Toy`)
- Ponechaný model `Location` pre spätnú kompatibilitu, ale už sa nepoužíva

### 2. Implementácia OpenStreetMap API

- Vytvorený klient pre Nominatim API (OpenStreetMap) v `src/lib/nominatim.ts`
- Implementované funkcie pre vyhľadávanie adries a získavanie geografických súradníc
- Implementované funkcie pre reverzné geocoding (získanie adresy zo súradníc)

### 3. Vytvorenie komponentu pre vyhľadávanie adries

- Vytvorený React komponent `AddressSearch` pre vyhľadávanie adries pomocou OpenStreetMap
- Implementovaný našepkávač adries s výsledkami z OpenStreetMap API
- Umožnenie výberu adresy a uloženie jej súradníc

### 4. Úprava profilu používateľa

- Aktualizovaný formulár profilu používateľa, aby používal nový komponent pre vyhľadávanie adries
- Upravený API endpoint pre aktualizáciu profilu, aby ukladal súradnice

### 5. Odstránenie lokality z hračiek

- Upravené formuláre pre vytvorenie a editáciu hračiek - odstránené pole pre výber lokality
- Upravené API endpointy pre vytvorenie a aktualizáciu hračiek
- Upravená logika pre zobrazenie lokality hračky (použitá lokalita vlastníka)

### 6. Migrácia dát

- Vytvorený migračný skript `src/scripts/migrate_location_to_user.js` pre presun existujúcich lokalít hračiek k používateľom
- Vytvorený SQL skript `src/prisma/migrations/migration_location_to_user.sql` pre pridanie stĺpcov do tabuľky User

### 7. Aktualizácia zobrazenia detailu hračky

- Upravené zobrazenie detailu hračky, aby zobrazovalo lokalitu vlastníka namiesto lokality hračky

## Postup migrácie

1. Aplikovať Prisma migráciu: `npx prisma migrate dev --name remove_toy_location`
2. Spustiť migračný skript: `node src/scripts/migrate_location_to_user.js`
3. Reštartovať aplikáciu

## Poznámky

- Model `Location` je ponechaný pre spätnú kompatibilitu, ale už sa nepoužíva
- V budúcej verzii bude model `Location` úplne odstránený
- Všetky existujúce hračky budú používať lokalitu vlastníka
